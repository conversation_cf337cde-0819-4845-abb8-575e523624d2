import Foundation
import CoreData
import SwiftUI

class NotesManager: ObservableObject {
    static let shared = NotesManager()
    
    @Published var notes: [Note] = []
    @Published var folders: [Folder] = []
    @Published var selectedNote: Note?
    @Published var selectedFolder: Folder?
    
    private var context: NSManagedObjectContext {
        PersistenceController.shared.container.viewContext
    }
    
    init() {
        loadNotes()
        loadFolders()

        // For testing: Auto-select first note if available
        if !notes.isEmpty {
            selectedNote = notes.first
            NSLog("📝 NEXUS DEBUG: NotesManager - Auto-selected first note for testing: \(notes.first?.title ?? "Untitled")")
        }
    }
    
    // MARK: - Notes Management
    
    func createNote(title: String = "", content: String = "", in folder: Folder? = nil) -> Note {
        let note = Note(context: context)
        note.uuid = UUID()
        note.title = title.isEmpty ? nil : title
        note.content = content
        note.creationDate = Date()
        note.modifiedDate = Date()
        note.isMarkdownEnabled = false
        
        if let folder = folder {
            note.addToFolders(folder)
        }
        
        saveContext()
        loadNotes()
        
        return note
    }
    
    func updateNote(_ note: Note, title: String? = nil, content: String? = nil) {
        if let title = title {
            note.title = title.isEmpty ? nil : title
        }
        
        if let content = content {
            note.content = content
        }
        
        note.modifiedDate = Date()
        saveContext()
        
        objectWillChange.send()
    }
    
    func deleteNote(_ note: Note) {
        context.delete(note)
        saveContext()
        loadNotes()
        
        if selectedNote == note {
            selectedNote = nil
        }
    }
    
    func toggleMarkdown(for note: Note) {
        note.isMarkdownEnabled.toggle()
        note.modifiedDate = Date()
        saveContext()
        
        objectWillChange.send()
    }
    
    func duplicateNote(_ note: Note) -> Note {
        let newNote = createNote(
            title: (note.title ?? "Untitled") + " Copy",
            content: note.content ?? ""
        )
        
        // Copy folder associations
        if let folders = note.folders as? Set<Folder> {
            for folder in folders {
                newNote.addToFolders(folder)
            }
        }
        
        newNote.isMarkdownEnabled = note.isMarkdownEnabled
        saveContext()
        
        return newNote
    }
    
    // MARK: - Folders Management
    
    func createFolder(name: String, parent: Folder? = nil) -> Folder {
        let folder = Folder(context: context)
        folder.uuid = UUID()
        folder.name = name
        folder.creationDate = Date()
        folder.parentFolder = parent
        
        saveContext()
        loadFolders()
        
        return folder
    }
    
    func updateFolder(_ folder: Folder, name: String) {
        folder.name = name
        saveContext()
        
        objectWillChange.send()
    }
    
    func deleteFolder(_ folder: Folder) {
        // Move notes to parent folder or remove folder association
        if let notes = folder.notes as? Set<Note> {
            for note in notes {
                note.removeFromFolders(folder)
                if let parentFolder = folder.parentFolder {
                    note.addToFolders(parentFolder)
                }
            }
        }
        
        // Move subfolders to parent
        if let subfolders = folder.subfolders as? Set<Folder> {
            for subfolder in subfolders {
                subfolder.parentFolder = folder.parentFolder
            }
        }
        
        context.delete(folder)
        saveContext()
        loadFolders()
        
        if selectedFolder == folder {
            selectedFolder = nil
        }
    }
    
    func moveNote(_ note: Note, to folder: Folder?) {
        // Remove from all current folders
        if let currentFolders = note.folders as? Set<Folder> {
            for currentFolder in currentFolders {
                note.removeFromFolders(currentFolder)
            }
        }
        
        // Add to new folder if specified
        if let folder = folder {
            note.addToFolders(folder)
        }
        
        note.modifiedDate = Date()
        saveContext()
        
        objectWillChange.send()
    }
    
    // MARK: - Data Loading
    
    private func loadNotes() {
        let request = NSFetchRequest<Note>(entityName: "Note")
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Note.modifiedDate, ascending: false)
        ]
        
        do {
            notes = try context.fetch(request)
            NSLog("📝 NEXUS DEBUG: NotesManager - Loaded \(notes.count) notes")
        } catch {
            NSLog("❌ NEXUS DEBUG: NotesManager - Failed to load notes: \(error)")
            notes = []
        }
    }
    
    private func loadFolders() {
        let request = NSFetchRequest<Folder>(entityName: "Folder")
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Folder.name, ascending: true)
        ]
        
        do {
            folders = try context.fetch(request)
        } catch {
            print("Failed to load folders: \(error)")
            folders = []
        }
    }
    
    private func saveContext() {
        do {
            try context.save()
        } catch {
            print("Failed to save context: \(error)")
        }
    }
    
    // MARK: - Utility Methods
    
    func getNotesInFolder(_ folder: Folder?) -> [Note] {
        if let folder = folder {
            return folder.notesArray
        } else {
            // Return notes not in any folder
            return notes.filter { note in
                (note.folders as? Set<Folder>)?.isEmpty ?? true
            }
        }
    }
    
    func getRootFolders() -> [Folder] {
        return folders.filter { $0.parentFolder == nil }
    }
    
    func searchNotes(query: String) -> [Note] {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return notes
        }
        
        let lowercaseQuery = query.lowercased()
        
        return notes.filter { note in
            let titleMatch = note.title?.lowercased().contains(lowercaseQuery) ?? false
            let contentMatch = note.content?.lowercased().contains(lowercaseQuery) ?? false
            return titleMatch || contentMatch
        }
    }
    
    func getRecentNotes(limit: Int = 10) -> [Note] {
        return Array(notes.prefix(limit))
    }
}