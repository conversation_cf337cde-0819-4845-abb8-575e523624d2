import SwiftUI
import CoreData

struct ContentView: View {
    @EnvironmentObject private var persistenceController: PersistenceController
    @StateObject private var searchService = SearchService.shared
    @State private var searchText = ""
    @State private var selectedTab = 0
    @State private var showSearchResults = false
    
    var body: some View {
        VStack(spacing: 0) {
            searchBar
            
            if showSearchResults {
                searchResultsView
            } else {
                TabView(selection: $selectedTab) {
                NotesView()
                    .tabItem {
                        Image(systemName: "doc.text")
                        Text("Notes")
                    }
                    .tag(0)
                
                CalculatorView()
                    .tabItem {
                        Image(systemName: "function")
                        Text("Calculator")
                    }
                    .tag(1)
                
                ClipboardView()
                    .tabItem {
                        Image(systemName: "doc.on.clipboard")
                        Text("Clipboard")
                    }
                    .tag(2)
                
                LauncherView()
                    .tabItem {
                        Image(systemName: "rocket")
                        Text("Launcher")
                    }
                    .tag(3)
                }
                .frame(width: 800, height: 550)
            }
        }
        .background(Color(NSColor.windowBackgroundColor))
        .onChange(of: searchText) { newValue in
            if newValue.isEmpty {
                showSearchResults = false
            } else {
                showSearchResults = true
                searchService.search(query: newValue)
            }
        }
    }
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search everything...", text: $searchText)
                .textFieldStyle(.plain)
                .font(.system(size: 16))
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8, corners: [.topLeft, .topRight])
    }
    
    private var searchResultsView: some View {
        VStack {
            if searchService.isSearching {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Searching...")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if searchService.results.isEmpty {
                VStack {
                    Image(systemName: "magnifyingglass")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    Text("No results found")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Try a different search term")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(searchService.results) { result in
                    SearchResultRow(result: result) {
                        handleSearchResultSelection(result)
                    }
                }
                .listStyle(.plain)
            }
        }
        .frame(width: 800, height: 550)
    }
    
    private func handleSearchResultSelection(_ result: SearchResult) {
        searchText = ""
        showSearchResults = false
        
        switch result.type {
        case .note:
            selectedTab = 0
        case .folder:
            selectedTab = 0
        case .clipboardItem:
            selectedTab = 2
        case .launcherItem:
            selectedTab = 3
        }
    }
}

struct SearchResultRow: View {
    let result: SearchResult
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: result.type.icon)
                .foregroundColor(.accentColor)
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(result.title)
                    .font(.headline)
                    .lineLimit(1)
                
                Text(result.snippet)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Text(result.type.rawValue)
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(8)
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

struct NotesView: View {
    @StateObject private var notesManager = NotesManager.shared
    @State private var editingText = ""
    @State private var showingNewFolderAlert = false
    @State private var newFolderName = ""
    
    var body: some View {
        HStack(spacing: 0) {
            // Sidebar
            VStack(alignment: .leading, spacing: 0) {
                // Sidebar header
                HStack {
                    Text("Notes")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: createNewNote) {
                        Image(systemName: "plus")
                    }
                    .buttonStyle(.plain)
                    .help("New Note")
                    
                    Button(action: { showingNewFolderAlert = true }) {
                        Image(systemName: "folder.badge.plus")
                    }
                    .buttonStyle(.plain)
                    .help("New Folder")
                }
                .padding()
                
                Divider()
                
                // Folders and notes list
                List(selection: $notesManager.selectedNote) {
                    // Root folders
                    ForEach(notesManager.getRootFolders(), id: \.uuid) { folder in
                        FolderRowView(folder: folder)
                    }
                    
                    // Notes not in any folder
                    let unorganizedNotes = notesManager.getNotesInFolder(nil)
                    if !unorganizedNotes.isEmpty {
                        Section("Notes") {
                            ForEach(unorganizedNotes, id: \.uuid) { note in
                                NoteRowView(note: note)
                                    .tag(note)
                            }
                        }
                    }
                }
                .listStyle(.sidebar)
            }
            .frame(width: 250)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // Editor
            VStack(spacing: 0) {
                if let selectedNote = notesManager.selectedNote {
                    let _ = NSLog("📝 NEXUS DEBUG: ContentView - selectedNote found, creating PlainTextEditor")
                    // Editor header
                    HStack {
                        TextField("Note title", text: Binding(
                            get: { selectedNote.title ?? "" },
                            set: { notesManager.updateNote(selectedNote, title: $0) }
                        ))
                        .textFieldStyle(.plain)
                        .font(.title2)
                        .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Button(action: { notesManager.toggleMarkdown(for: selectedNote) }) {
                            Image(systemName: selectedNote.isMarkdownEnabled ? "text.badge.checkmark" : "text.badge.plus")
                        }
                        .buttonStyle(.plain)
                        .help(selectedNote.isMarkdownEnabled ? "Disable Markdown" : "Enable Markdown")
                        
                        Button(action: { duplicateNote(selectedNote) }) {
                            Image(systemName: "doc.on.doc")
                        }
                        .buttonStyle(.plain)
                        .help("Duplicate Note")
                        
                        Button(action: { deleteNote(selectedNote) }) {
                            Image(systemName: "trash")
                        }
                        .buttonStyle(.plain)
                        .help("Delete Note")
                    }
                    .padding()
                    
                    Divider()
                    
                    // Text editor
                    PlainTextEditor(
                        text: Binding(
                            get: { selectedNote.content ?? "" },
                            set: { content in
                                editingText = content
                                notesManager.updateNote(selectedNote, content: content)
                            }
                        ),
                        isMarkdownEnabled: selectedNote.isMarkdownEnabled,
                        onTextChange: { newText in
                            notesManager.updateNote(selectedNote, content: newText)
                        }
                    )
                    // CRITICAL FIX: Removed .cornerRadius(4) as it may interfere with NSTextView rendering
                } else {
                    let _ = NSLog("📝 NEXUS DEBUG: ContentView - no selectedNote, showing placeholder")
                    // No note selected
                    VStack {
                        Image(systemName: "doc.text")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)
                        
                        Text("Select a note to edit")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("Or create a new note to get started")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Button("Create New Note") {
                            createNewNote()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
        }
        .alert("New Folder", isPresented: $showingNewFolderAlert) {
            TextField("Folder name", text: $newFolderName)
            Button("Create") {
                if !newFolderName.isEmpty {
                    _ = notesManager.createFolder(name: newFolderName)
                    newFolderName = ""
                }
            }
            Button("Cancel", role: .cancel) {
                newFolderName = ""
            }
        }
    }
    
    private func createNewNote() {
        NSLog("📝 NEXUS DEBUG: ContentView - createNewNote called")
        let newNote = notesManager.createNote(
            title: "New Note",
            content: "",
            in: notesManager.selectedFolder
        )
        notesManager.selectedNote = newNote
        NSLog("📝 NEXUS DEBUG: ContentView - new note created and selected")
    }
    
    private func duplicateNote(_ note: Note) {
        let duplicatedNote = notesManager.duplicateNote(note)
        notesManager.selectedNote = duplicatedNote
    }
    
    private func deleteNote(_ note: Note) {
        notesManager.deleteNote(note)
    }
}

struct NoteRowView: View {
    let note: Note
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(note.displayTitle)
                .font(.subheadline)
                .fontWeight(.medium)
                .lineLimit(1)
            
            if let content = note.content, !content.isEmpty {
                Text(content)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            if let modifiedDate = note.modifiedDate {
                Text(modifiedDate, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary.opacity(0.7))
            }
        }
        .padding(.vertical, 2)
    }
}

struct FolderRowView: View {
    let folder: Folder
    @StateObject private var notesManager = NotesManager.shared
    
    var body: some View {
        DisclosureGroup {
            // Subfolders
            ForEach(folder.subfoldersArray, id: \.uuid) { subfolder in
                FolderRowView(folder: subfolder)
            }
            
            // Notes in this folder
            ForEach(folder.notesArray, id: \.uuid) { note in
                NoteRowView(note: note)
                    .tag(note)
            }
        } label: {
            HStack {
                Image(systemName: "folder")
                    .foregroundColor(.accentColor)
                Text(folder.name ?? "Untitled Folder")
                    .font(.subheadline)
                Spacer()
                Text("\(folder.notesArray.count)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct CalculatorView: View {
    @State private var input = ""
    @State private var result = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Smart Calculator")
                    .font(.headline)
                
                TextField("Enter expression or natural language...", text: $input)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        calculateResult()
                    }
                
                if !result.isEmpty {
                    Text("Result: \(result)")
                        .font(.title2)
                        .foregroundColor(.primary)
                }
            }
            
            Spacer()
        }
        .padding()
    }
    
    private func calculateResult() {
        let expression = NSExpression(format: input)
        
        // NSExpression doesn't throw, but can return nil for invalid expressions
        guard let value = expression.expressionValue(with: nil, context: nil) else {
            result = "Error"
            return
        }
        
        result = "\(value)"
    }
}

struct ClipboardView: View {
    @StateObject private var clipboardService = ClipboardService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                Text("Clipboard History")
                    .font(.headline)
                
                Spacer()
                
                Button("Clear All") {
                    clipboardService.clearHistory()
                }
                .buttonStyle(.plain)
                .foregroundColor(.red)
            }
            .padding()
            
            Divider()
            
            // Clipboard items list
            if clipboardService.recentItems.isEmpty {
                VStack {
                    Image(systemName: "doc.on.clipboard")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    
                    Text("No clipboard history")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Copy something to see it here")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(clipboardService.recentItems, id: \.uuid) { item in
                        ClipboardItemRow(item: item, clipboardService: clipboardService)
                    }
                }
                .listStyle(.plain)
            }
        }
    }
}

struct ClipboardItemRow: View {
    let item: ClipboardItem
    let clipboardService: ClipboardService
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: clipboardService.getIcon(for: item))
                .foregroundColor(.accentColor)
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(clipboardService.getDisplayContent(for: item))
                    .font(.subheadline)
                    .lineLimit(3)
                
                HStack {
                    if let sourceApp = item.sourceApp {
                        Text("From \(sourceApp)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if let timestamp = item.timestamp {
                        Text(timestamp, style: .relative)
                            .font(.caption2)
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                }
            }
            
            Spacer()
            
            Button(action: { clipboardService.copyItemToPasteboard(item) }) {
                Image(systemName: "doc.on.clipboard")
            }
            .buttonStyle(.plain)
            .help("Copy to clipboard")
            
            Button(action: { clipboardService.deleteItem(item) }) {
                Image(systemName: "trash")
            }
            .buttonStyle(.plain)
            .foregroundColor(.red)
            .help("Delete")
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            clipboardService.copyItemToPasteboard(item)
        }
    }
}

struct LauncherView: View {
    var body: some View {
        VStack {
            Text("Quick Launcher")
                .font(.headline)
            
            Text("Drag apps and files here to create shortcuts")
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: RectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RectCorner: OptionSet {
    let rawValue: Int
    
    static let topLeft = RectCorner(rawValue: 1 << 0)
    static let topRight = RectCorner(rawValue: 1 << 1)
    static let bottomLeft = RectCorner(rawValue: 1 << 2)
    static let bottomRight = RectCorner(rawValue: 1 << 3)
    
    static let allCorners: RectCorner = [.topLeft, .topRight, .bottomLeft, .bottomRight]
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: RectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let topLeft = corners.contains(.topLeft) ? radius : 0
        let topRight = corners.contains(.topRight) ? radius : 0
        let bottomLeft = corners.contains(.bottomLeft) ? radius : 0
        let bottomRight = corners.contains(.bottomRight) ? radius : 0
        
        path.move(to: CGPoint(x: rect.minX + topLeft, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX - topRight, y: rect.minY))
        path.addArc(center: CGPoint(x: rect.maxX - topRight, y: rect.minY + topRight), 
                   radius: topRight, startAngle: Angle(degrees: -90), endAngle: Angle(degrees: 0), clockwise: false)
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - bottomRight))
        path.addArc(center: CGPoint(x: rect.maxX - bottomRight, y: rect.maxY - bottomRight), 
                   radius: bottomRight, startAngle: Angle(degrees: 0), endAngle: Angle(degrees: 90), clockwise: false)
        path.addLine(to: CGPoint(x: rect.minX + bottomLeft, y: rect.maxY))
        path.addArc(center: CGPoint(x: rect.minX + bottomLeft, y: rect.maxY - bottomLeft), 
                   radius: bottomLeft, startAngle: Angle(degrees: 90), endAngle: Angle(degrees: 180), clockwise: false)
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY + topLeft))
        path.addArc(center: CGPoint(x: rect.minX + topLeft, y: rect.minY + topLeft), 
                   radius: topLeft, startAngle: Angle(degrees: 180), endAngle: Angle(degrees: 270), clockwise: false)
        
        return path
    }
}

#Preview {
    ContentView()
        .environmentObject(PersistenceController.shared)
}