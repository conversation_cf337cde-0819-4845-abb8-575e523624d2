import SwiftUI
import AppKit

struct PlainTextEditor: NSViewRepresentable {
    @Binding var text: String
    var isMarkdownEnabled: Bool = false
    var onTextChange: ((String) -> Void)?

    typealias Context = NSViewRepresentableContext<PlainTextEditor>
    
    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = FocusableTextView()
        
        NSLog("🔨 NEXUS DEBUG: Creating NSTextView - isEditable: \(textView.isEditable), isSelectable: \(textView.isSelectable)")
        
        // Configure text view for plain text editing - following PRD specifications
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false // User configurable per PRD
        textView.smartInsertDeleteEnabled = false
        textView.isRichText = false
        textView.usesRuler = false
        textView.allowsUndo = true
        textView.isEditable = true
        textView.isSelectable = true
        textView.isVerticallyResizable = true
        textView.usesFindBar = true
        textView.isAutomaticLinkDetectionEnabled = false
        textView.isAutomaticDataDetectionEnabled = false
        
        // Enhanced configuration for better focus and performance
        textView.textContainerInset = NSSize(width: 12, height: 12)
        textView.usesInspectorBar = false
        
        NSLog("🔨 NEXUS DEBUG: After configuration - isEditable: \(textView.isEditable), isSelectable: \(textView.isSelectable)")
        
        // Set font and colors for better visibility with explicit contrast
        textView.font = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular)

        // Use explicit colors with guaranteed contrast for text visibility
        if #available(macOS 10.14, *) {
            // Use system colors that adapt to appearance but ensure contrast
            textView.textColor = NSColor.textColor
            textView.backgroundColor = NSColor.textBackgroundColor
        } else {
            // Fallback for older macOS versions
            textView.textColor = NSColor.black
            textView.backgroundColor = NSColor.white
        }

        textView.insertionPointColor = NSColor.controlAccentColor

        // Ensure proper appearance and force background drawing
        textView.drawsBackground = true
        textView.isRichText = false

        NSLog("🎨 NEXUS DEBUG: Text colors set - textColor: \(textView.textColor?.description ?? "nil"), backgroundColor: \(textView.backgroundColor.description ?? "nil")")
        
        // Set up text container with proper sizing
        if let textContainer = textView.textContainer {
            textContainer.containerSize = CGSize(width: 0, height: CGFloat.greatestFiniteMagnitude)
            textContainer.widthTracksTextView = true
            textContainer.heightTracksTextView = false
            textContainer.lineFragmentPadding = 8
        }
        
        // Ensure layout manager settings
        if let layoutManager = textView.layoutManager {
            layoutManager.allowsNonContiguousLayout = false
        }
        
        // Configure scroll view
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.borderType = .noBorder
        
        // Focus handling is now done in mouseDown method to avoid conflicts with window dragging
        
        // Set initial text with proper attributes - CRITICAL for visibility
        setTextWithAttributes(textView: textView, text: text)

        // Force layout and display
        textView.sizeToFit()
        textView.needsLayout = true
        
        // Set delegate
        textView.delegate = context.coordinator
        
        // Store reference to text view in coordinator for focus management
        context.coordinator.textView = textView
        
        // Auto-focus the text view when a note is selected for better UX
        // This ensures users can immediately start typing when they select a note
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let window = textView.window {
                let success = window.makeFirstResponder(textView)
                NSLog("🎯 NEXUS DEBUG: Auto-focused text view: \(success)")
            }
        }

        NSLog("🎯 NEXUS DEBUG: Text view created and configured")

        return scrollView
    }
    
    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }

        if textView.string != text {
            let currentSelection = textView.selectedRange()

            // Use the same method as initial setup to ensure consistency
            setTextWithAttributes(textView: textView, text: text)

            // Restore selection if valid
            let maxLength = textView.string.count
            let safeLocation = min(currentSelection.location, maxLength)
            let safeLength = min(currentSelection.length, maxLength - safeLocation)
            let safeRange = NSRange(location: safeLocation, length: safeLength)
            textView.setSelectedRange(safeRange)
        }

        // Update markdown rendering if needed
        if isMarkdownEnabled {
            applyMarkdownStyling(to: textView)
        } else {
            removeMarkdownStyling(from: textView)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // MARK: - Helper Methods

    private func setTextWithAttributes(textView: NSTextView, text: String) {
        // Use consistent colors with guaranteed contrast
        let textColor: NSColor
        let backgroundColor: NSColor

        if #available(macOS 10.14, *) {
            textColor = NSColor.textColor
            backgroundColor = NSColor.textBackgroundColor
        } else {
            textColor = NSColor.black
            backgroundColor = NSColor.white
        }

        // Create attributed string with proper attributes
        let attributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.monospacedSystemFont(ofSize: 14, weight: .regular),
            .foregroundColor: textColor
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)

        // Set the attributed string directly to text storage
        textView.textStorage?.setAttributedString(attributedString)

        // Ensure background color is set
        textView.backgroundColor = backgroundColor
        textView.textColor = textColor
        textView.insertionPointColor = NSColor.controlAccentColor

        // Force display update
        textView.needsDisplay = true

        NSLog("🎨 NEXUS DEBUG: Set text with attributes - length: \(text.count), textColor: \(textColor), backgroundColor: \(backgroundColor)")
    }
    
    private func applyMarkdownStyling(to textView: NSTextView) {
        let text = textView.string
        let attributedString = NSMutableAttributedString(string: text)
        
        // Reset to default attributes
        let defaultAttributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.systemFont(ofSize: 13),
            .foregroundColor: NSColor.textColor
        ]
        attributedString.addAttributes(defaultAttributes, range: NSRange(location: 0, length: text.count))
        
        // Apply markdown-style formatting
        applyHeaderStyling(to: attributedString)
        applyBoldStyling(to: attributedString)
        applyItalicStyling(to: attributedString)
        applyCodeStyling(to: attributedString)
        
        textView.textStorage?.setAttributedString(attributedString)
    }
    
    private func removeMarkdownStyling(from textView: NSTextView) {
        let text = textView.string
        let plainAttributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.monospacedSystemFont(ofSize: 14, weight: .regular),
            .foregroundColor: NSColor.labelColor
        ]
        let attributedString = NSAttributedString(string: text, attributes: plainAttributes)
        textView.textStorage?.setAttributedString(attributedString)
    }
    
    private func applyHeaderStyling(to attributedString: NSMutableAttributedString) {
        let text = attributedString.string
        let headerRegex = try! NSRegularExpression(pattern: "^(#{1,6})\\s+(.+)$", options: [.anchorsMatchLines])
        let matches = headerRegex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))
        
        for match in matches {
            let headerLevel = text[Range(match.range(at: 1), in: text)!].count
            let fontSize: CGFloat = max(16, 24 - CGFloat(headerLevel) * 2)
            
            attributedString.addAttributes([
                .font: NSFont.boldSystemFont(ofSize: fontSize),
                .foregroundColor: NSColor.labelColor
            ], range: match.range)
        }
    }
    
    private func applyBoldStyling(to attributedString: NSMutableAttributedString) {
        let text = attributedString.string
        let boldRegex = try! NSRegularExpression(pattern: "\\*\\*(.+?)\\*\\*", options: [])
        let matches = boldRegex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))
        
        for match in matches.reversed() {
            attributedString.addAttributes([
                .font: NSFont.boldSystemFont(ofSize: 13)
            ], range: match.range(at: 1))
        }
    }
    
    private func applyItalicStyling(to attributedString: NSMutableAttributedString) {
        let text = attributedString.string
        let italicRegex = try! NSRegularExpression(pattern: "\\*(.+?)\\*", options: [])
        let matches = italicRegex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))
        
        for match in matches.reversed() {
            let italicFont = NSFontManager.shared.convert(NSFont.systemFont(ofSize: 13), toHaveTrait: .italicFontMask)
            attributedString.addAttributes([
                .font: italicFont
            ], range: match.range(at: 1))
        }
    }
    
    private func applyCodeStyling(to attributedString: NSMutableAttributedString) {
        let text = attributedString.string
        let codeRegex = try! NSRegularExpression(pattern: "`(.+?)`", options: [])
        let matches = codeRegex.matches(in: text, options: [], range: NSRange(location: 0, length: text.count))
        
        for match in matches.reversed() {
            attributedString.addAttributes([
                .font: NSFont.monospacedSystemFont(ofSize: 12, weight: .regular),
                .backgroundColor: NSColor.controlBackgroundColor,
                .foregroundColor: NSColor.systemBlue
            ], range: match.range(at: 1))
        }
    }
}

extension PlainTextEditor {
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: PlainTextEditor
        var textView: NSTextView?
        
        init(_ parent: PlainTextEditor) {
            self.parent = parent
        }
        
        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }

            NSLog("📝 NEXUS DEBUG: Text changed: '\(textView.string.prefix(50))...' (length: \(textView.string.count))")

            // Apply proper attributes to ensure text is visible - use consistent method
            let fullRange = NSRange(location: 0, length: textView.string.count)

            // Use consistent colors with guaranteed contrast
            let textColor: NSColor
            if #available(macOS 10.14, *) {
                textColor = NSColor.textColor
            } else {
                textColor = NSColor.black
            }

            let attributes: [NSAttributedString.Key: Any] = [
                .font: NSFont.monospacedSystemFont(ofSize: 14, weight: .regular),
                .foregroundColor: textColor
            ]

            // Apply attributes to the full range to ensure visibility
            textView.textStorage?.addAttributes(attributes, range: fullRange)
            textView.insertionPointColor = NSColor.controlAccentColor
            textView.needsDisplay = true

            NSLog("📝 NEXUS DEBUG: Applied attributes to ensure text visibility")

            // Update the parent binding - avoid infinite loops
            let newText = textView.string
            if self.parent.text != newText {
                DispatchQueue.main.async {
                    self.parent.text = newText
                    self.parent.onTextChange?(newText)
                }
            }
        }
        
        func textViewDidChangeSelection(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }
            print("🎯 Selection changed - cursor at: \(textView.selectedRange().location), isFirstResponder: \(textView.window?.firstResponder === textView)")
        }
        
        func textView(_ textView: NSTextView, doCommandBy commandSelector: Selector) -> Bool {
            print("🎹 doCommandBy called with selector: \(commandSelector)")
            
            // Handle special key combinations
            if commandSelector == #selector(NSTextView.insertTab(_:)) {
                // Insert 4 spaces instead of tab for better plain text editing
                textView.insertText("    ", replacementRange: NSRange(location: textView.selectedRange().location, length: 0))
                return true
            }
            
            return false
        }
        
        func textView(_ textView: NSTextView, shouldChangeTextIn affectedCharRange: NSRange, replacementString: String?) -> Bool {
            print("🎹 shouldChangeTextIn called - range: \(affectedCharRange), replacement: \(replacementString ?? "nil")")
            return true
        }
        

    }
}

class FocusableTextView: NSTextView {
    override var acceptsFirstResponder: Bool {
        NSLog("🎯 NEXUS DEBUG: FocusableTextView acceptsFirstResponder called - returning true")
        return true
    }
    
    override func becomeFirstResponder() -> Bool {
        NSLog("🎯 NEXUS DEBUG: FocusableTextView becomeFirstResponder called")
        let result = super.becomeFirstResponder()
        NSLog("🎯 NEXUS DEBUG: FocusableTextView becomeFirstResponder result: \(result)")

        // Ensure cursor is visible and text is properly displayed
        if result {
            self.needsDisplay = true
            self.setNeedsDisplay(self.bounds)

            // Ensure insertion point is visible
            self.insertionPointColor = NSColor.controlAccentColor

            // Force cursor to appear at the end if no selection
            if self.selectedRange().length == 0 && self.selectedRange().location == 0 && !self.string.isEmpty {
                self.setSelectedRange(NSRange(location: self.string.count, length: 0))
            }

            NSLog("🎯 NEXUS DEBUG: First responder setup complete - cursor at: \(self.selectedRange().location)")
        }

        return result
    }
    
    override func keyDown(with event: NSEvent) {
        NSLog("🎹 NEXUS DEBUG: FocusableTextView keyDown called with key: \(event.charactersIgnoringModifiers ?? "nil")")
        super.keyDown(with: event)
    }
    
    override func mouseDown(with event: NSEvent) {
        NSLog("🖱️ NEXUS DEBUG: FocusableTextView mouseDown called")

        // Let super handle the mouse down first
        super.mouseDown(with: event)

        // Only become first responder if the click was actually in our bounds
        // This prevents interfering with window dragging
        let localPoint = self.convert(event.locationInWindow, from: nil)
        if self.bounds.contains(localPoint) {
            // User clicked in the text area, they want to edit
            if let firstResponder = self.window?.firstResponder {
                if !firstResponder.isEqual(self) {
                    let success = self.window?.makeFirstResponder(self) ?? false
                    print("🖱️ makeFirstResponder result: \(success)")
                }
            } else {
                let success = self.window?.makeFirstResponder(self) ?? false
                print("🖱️ makeFirstResponder result: \(success)")
            }

            // Force display update to show cursor
            self.needsDisplay = true

            // Log cursor position after click
            DispatchQueue.main.async {
                print("🖱️ Cursor position after click: \(self.selectedRange().location)")
            }
        } else {
            print("🖱️ Click outside text bounds, not taking focus")
        }
    }
    
    override func viewDidMoveToWindow() {
        super.viewDidMoveToWindow()

        // Don't automatically steal focus - this interferes with window dragging
        // Let the user click to focus when they want to edit
        if self.window != nil {
            // Just ensure visibility without stealing focus
            self.needsDisplay = true
            self.enclosingScrollView?.needsDisplay = true

            print("🎯 Text view added to window, ready for user interaction")
        }
    }
    
    override func insertText(_ string: Any, replacementRange: NSRange) {
        print("📝 insertText called with: \(string)")
        super.insertText(string, replacementRange: replacementRange)

        // Ensure text remains visible after insertion by applying proper attributes
        let fullRange = NSRange(location: 0, length: self.string.count)

        // Use consistent colors with guaranteed contrast
        let textColor: NSColor
        if #available(macOS 10.14, *) {
            textColor = NSColor.textColor
        } else {
            textColor = NSColor.black
        }

        let attributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.monospacedSystemFont(ofSize: 14, weight: .regular),
            .foregroundColor: textColor
        ]

        // Apply attributes to ensure visibility
        self.textStorage?.addAttributes(attributes, range: fullRange)
        self.needsDisplay = true

        print("📝 Applied attributes to full text after insertion")
    }
}
