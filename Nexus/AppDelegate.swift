import Cocoa
import SwiftUI

class AppDelegate: NSObject, NSApplicationDelegate, NSWindowDelegate {
    private var statusItem: NSStatusItem?
    private var popover: NSPopover?
    private var detachedWindow: NSWindow?
    private var globalHotkeyService: GlobalHotkeyService?
    private var clipboardService: ClipboardService?
    private var isDetachedMode = false
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        setupMenuBar()
        setupPopover()
        setupGlobalHotkey()
        setupClipboardService()
        
        // Set as menu bar accessory app
        NSApp.setActivationPolicy(.accessory)
        
        // Debug: Print to console to confirm app is running
        print("✅ Nexus started successfully!")
        print("📍 Look for document icon in menu bar")
        print("⌨️ Use Cmd+Option+Space to open")
        
        // App is ready - no auto-showing needed
        print("🎯 App ready! Use menu bar icon or Cmd+Option+Space to open")
    }
    
    private func setupClipboardService() {
        clipboardService = ClipboardService.shared
    }
    
    private func setupGlobalHotkey() {
        globalHotkeyService = GlobalHotkeyService.shared
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleGlobalHotkey),
            name: .globalHotkeyPressed,
            object: nil
        )
    }
    
    @objc private func handleGlobalHotkey() {
        print("🔥 Global hotkey pressed!")
        togglePopover()
    }
    
    private func setupMenuBar() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let statusButton = statusItem?.button {
            statusButton.image = NSImage(systemSymbolName: "doc.text", accessibilityDescription: "Nexus")
            statusButton.action = #selector(togglePopover)
            statusButton.target = self

            // Add right-click context menu
            let menu = NSMenu()
            menu.addItem(NSMenuItem(title: "Open in Popover", action: #selector(showPopover), keyEquivalent: ""))
            menu.addItem(NSMenuItem(title: "Open in Window", action: #selector(showDetachedWindow), keyEquivalent: ""))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(NSMenuItem(title: "Quit Nexus", action: #selector(NSApplication.terminate(_:)), keyEquivalent: "q"))

            // Set menu items' targets
            for item in menu.items {
                if item.action == #selector(showPopover) || item.action == #selector(showDetachedWindow) {
                    item.target = self
                }
            }

            statusButton.menu = menu

            NSLog("✅ NEXUS DEBUG: Menu bar icon created successfully")
        } else {
            NSLog("❌ NEXUS DEBUG: Failed to create menu bar icon")
        }
    }

    @objc private func showPopover() {
        isDetachedMode = false
        if detachedWindow?.isVisible == true {
            detachedWindow?.orderOut(nil)
        }
        togglePopoverMode()
    }

    @objc private func showDetachedWindow() {
        if popover?.isShown == true {
            popover?.performClose(nil)
        }
        toggleDetachedWindow()
    }
    
    private func setupPopover() {
        let contentView = ContentView()
            .environmentObject(PersistenceController.shared)

        popover = NSPopover()
        popover?.contentViewController = NSHostingController(rootView: contentView)
        popover?.behavior = .applicationDefined
        popover?.contentSize = NSSize(width: 800, height: 600)

        // Modern appearance for better system integration
        if #available(macOS 10.14, *) {
            popover?.appearance = NSAppearance(named: .vibrantLight)
        }

        print("✅ Popover created successfully")
    }

    private func setupDetachedWindow() {
        let contentView = ContentView()
            .environmentObject(PersistenceController.shared)

        detachedWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )

        detachedWindow?.title = "Nexus"
        detachedWindow?.contentViewController = NSHostingController(rootView: contentView)
        detachedWindow?.center()
        detachedWindow?.setFrameAutosaveName("NexusMainWindow")

        // Make window movable and resizable
        detachedWindow?.isMovableByWindowBackground = true
        detachedWindow?.minSize = NSSize(width: 600, height: 400)

        // Ensure proper window behavior for dragging
        detachedWindow?.hasShadow = true
        detachedWindow?.level = .normal
        detachedWindow?.collectionBehavior = [.managed, .participatesInCycle]

        // Set delegate to handle window events
        detachedWindow?.delegate = self

        print("✅ Detached window created successfully")
    }
    
    @objc private func togglePopover() {
        print("🎯 togglePopover called!")

        // Check if we should use detached window mode (e.g., if Option key is held)
        let shouldUseDetachedMode = NSEvent.modifierFlags.contains(.option)

        if shouldUseDetachedMode || isDetachedMode {
            toggleDetachedWindow()
        } else {
            togglePopoverMode()
        }
    }

    private func togglePopoverMode() {
        guard let popover = popover else {
            NSLog("❌ NEXUS DEBUG: No popover found")
            return
        }

        guard let statusButton = statusItem?.button else {
            NSLog("❌ NEXUS DEBUG: No status button found")
            return
        }

        if popover.isShown {
            NSLog("🙈 NEXUS DEBUG: Hiding popover")
            popover.performClose(nil)
        } else {
            NSLog("👀 NEXUS DEBUG: Showing popover")
            popover.show(relativeTo: statusButton.bounds, of: statusButton, preferredEdge: .minY)

            // Activate the app for proper focus handling
            NSApp.activate(ignoringOtherApps: true)

            NSLog("✅ NEXUS DEBUG: Popover shown successfully")
        }
    }

    private func toggleDetachedWindow() {
        if detachedWindow == nil {
            setupDetachedWindow()
        }

        guard let window = detachedWindow else {
            print("❌ No detached window found")
            return
        }

        if window.isVisible {
            print("🙈 Hiding detached window")
            window.orderOut(nil)
            isDetachedMode = false
        } else {
            print("👀 Showing detached window")

            // Hide popover if it's showing
            if popover?.isShown == true {
                popover?.performClose(nil)
            }

            window.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            isDetachedMode = true

            // Ensure window can be dragged by giving it proper focus
            DispatchQueue.main.async {
                window.makeKey()
                print("🎯 Detached window made key for proper dragging")
            }

            print("✅ Detached window shown successfully")
        }
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return false
    }

    // MARK: - NSWindowDelegate

    func windowWillClose(_ notification: Notification) {
        if notification.object as? NSWindow === detachedWindow {
            isDetachedMode = false
            print("🙈 Detached window closed, returning to popover mode")
        }
    }

    func windowDidBecomeKey(_ notification: Notification) {
        if notification.object as? NSWindow === detachedWindow {
            print("🎯 Detached window became key")
        }
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        PersistenceController.shared.save()
    }
}